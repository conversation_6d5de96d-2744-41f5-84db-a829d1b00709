//+------------------------------------------------------------------+
//| MartingaleGroupTest.mq4                                          |
//| MartingaleGroup 類別測試腳本                                     |
//+------------------------------------------------------------------+
#property copyright "MQL4 Library"
#property version   "1.00"
#property strict

#include "StatisticsGroup copy.mqh"

//+------------------------------------------------------------------+
//| 測試參數                                                         |
//+------------------------------------------------------------------+
input string TestSection = "====== 測試參數 ======";
input string TestSymbol = "EURUSD";
input int TestMagicNumber = 12345;
input int TestMaxOrders = 10;
input int TestMaxLevels = 5;

//+------------------------------------------------------------------+
//| 全局變量                                                         |
//+------------------------------------------------------------------+
MartingaleGroup* g_martingaleGroup = NULL;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("=== MartingaleGroup 測試開始 ===");
    
    // 測試建構函數
    TestConstructor();
    
    // 測試基本功能
    TestBasicFunctionality();
    
    // 測試統計計算
    TestStatisticsCalculation();
    
    // 測試群組管理
    TestGroupManagement();
    
    // 測試錯誤處理
    TestErrorHandling();
    
    Print("=== MartingaleGroup 測試完成 ===");
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // 清理測試對象
    if (g_martingaleGroup != NULL)
    {
        delete g_martingaleGroup;
        g_martingaleGroup = NULL;
    }
    
    Print("測試清理完成");
}

//+------------------------------------------------------------------+
//| 測試建構函數                                                     |
//+------------------------------------------------------------------+
void TestConstructor()
{
    Print("--- 測試建構函數 ---");
    
    // 測試默認建構函數
    MartingaleGroup* defaultGroup = new MartingaleGroup();
    if (defaultGroup != NULL)
    {
        Print("✓ 默認建構函數測試通過");
        delete defaultGroup;
    }
    else
    {
        Print("✗ 默認建構函數測試失敗");
    }
    
    // 測試帶參數的建構函數
    g_martingaleGroup = new MartingaleGroup(TestSymbol, TestMagicNumber, TestMaxOrders, TestMaxLevels);
    if (g_martingaleGroup != NULL)
    {
        Print("✓ 帶參數建構函數測試通過");
        
        // 驗證初始狀態
        int reservedSize = g_martingaleGroup.GetReservedSize();
        if (reservedSize == 0)
        {
            Print("✓ 初始已分配群組數為 0");
        }
        else
        {
            Print("✗ 初始已分配群組數應為 0，實際為：", reservedSize);
        }
    }
    else
    {
        Print("✗ 帶參數建構函數測試失敗");
    }
}

//+------------------------------------------------------------------+
//| 測試基本功能                                                     |
//+------------------------------------------------------------------+
void TestBasicFunctionality()
{
    Print("--- 測試基本功能 ---");
    
    if (g_martingaleGroup == NULL)
    {
        Print("✗ MartingaleGroup 實例為空，無法測試基本功能");
        return;
    }
    
    // 測試 GetReservedSize
    int initialSize = g_martingaleGroup.GetReservedSize();
    Print("初始已分配群組數：", initialSize);
    
    // 測試 Refresh 方法
    g_martingaleGroup.Refresh();
    Print("✓ Refresh 方法執行完成");
    
    // 測試 GetMartingaleStatistics
    MartingaleStats stats = g_martingaleGroup.GetMartingaleStatistics();
    Print("Martingale 統計：");
    Print("  已分配群組數：", stats.reservedGroups);
    Print("  活躍群組數：", stats.activeGroups);
    Print("  空群組數：", stats.emptyGroups);
    Print("  活躍群組 ID：", stats.activeId);
    Print("  下一個可用 ID：", stats.nextId);
}

//+------------------------------------------------------------------+
//| 測試統計計算                                                     |
//+------------------------------------------------------------------+
void TestStatisticsCalculation()
{
    Print("--- 測試統計計算 ---");
    
    if (g_martingaleGroup == NULL)
    {
        Print("✗ MartingaleGroup 實例為空，無法測試統計計算");
        return;
    }
    
    // 獲取基類統計數據
    OrderGroupStats baseStats = g_martingaleGroup.GetStatistics();
    Print("基類統計數據：");
    Print("  總訂單數：", baseStats.totalOrders);
    Print("  總盈虧：", DoubleToString(baseStats.totalProfit, 2));
    Print("  總手數：", DoubleToString(baseStats.totalLots, 2));
    Print("  平均開倉價：", DoubleToString(baseStats.avgOpenPrice, Digits));
    Print("  最大盈利：", DoubleToString(baseStats.maxProfit, 2));
    Print("  最大虧損：", DoubleToString(baseStats.maxLoss, 2));
    
    Print("✓ 統計計算測試完成");
}

//+------------------------------------------------------------------+
//| 測試群組管理                                                     |
//+------------------------------------------------------------------+
void TestGroupManagement()
{
    Print("--- 測試群組管理 ---");
    
    if (g_martingaleGroup == NULL)
    {
        Print("✗ MartingaleGroup 實例為空，無法測試群組管理");
        return;
    }
    
    // 測試獲取不存在的群組
    TradingStatistics* nonExistentGroup = g_martingaleGroup.GetGroup("non_existent_id");
    if (nonExistentGroup == NULL)
    {
        Print("✓ 獲取不存在群組返回 NULL");
    }
    else
    {
        Print("✗ 獲取不存在群組應返回 NULL");
    }
    
    Print("✓ 群組管理測試完成");
}

//+------------------------------------------------------------------+
//| 測試錯誤處理                                                     |
//+------------------------------------------------------------------+
void TestErrorHandling()
{
    Print("--- 測試錯誤處理 ---");
    
    if (g_martingaleGroup == NULL)
    {
        Print("✗ MartingaleGroup 實例為空，無法測試錯誤處理");
        return;
    }
    
    // 測試空 ID 的錯誤處理
    TradingStatistics* emptyIdGroup = g_martingaleGroup.GetGroup("");
    if (emptyIdGroup == NULL)
    {
        Print("✓ 空 ID 錯誤處理正確");
    }
    else
    {
        Print("✗ 空 ID 應返回 NULL");
    }
    
    Print("✓ 錯誤處理測試完成");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // 測試腳本不需要處理 tick 事件
}
