# MartingaleGroup 類別實作總結

## 概述

本文檔總結了 MartingaleGroup 類別中所有已宣告但尚未實作的方法的詳細實作。MartingaleGroup 繼承自 TradingStatistics 基類，提供了 Martingale 策略的群組管理和統計計算功能。

## 實作的方法

### 1. 建構函數和解構函數

#### `MartingaleGroup(string symbol, int magicNumber, int maxOrders, int maxLevels)`
- **功能**：初始化 MartingaleGroup 實例
- **參數驗證**：確保 maxOrders 和 maxLevels 為正數
- **初始化**：設置基本參數、創建 TradingPool、重置統計數據
- **錯誤處理**：TradingPool 創建失敗時輸出錯誤信息

#### `~MartingaleGroup()`
- **功能**：清理所有資源
- **清理順序**：先清理群組映射，再刪除 TradingPool
- **內存管理**：確保所有動態分配的對象都被正確釋放

### 2. 私有統計計算方法

#### `UpdateStatistics()`
- **功能**：重新計算統計數據的主要協調方法
- **執行順序**：基本統計 → 盈虧統計 → 時間統計 → 平均值 → Martingale 統計
- **設計模式**：使用模板方法模式，協調各個統計計算步驟

#### `UpdateMartingaleStatistics()`
- **功能**：重新計算 Martingale 特定統計數據
- **實作**：調用 CalculateMartingaleStatistics() 方法
- **職責**：專門處理 Martingale 策略相關的統計更新

#### `CalculateBasicStatistics()`
- **功能**：基本統計計算，利用基類 TradingStatistics 的方法
- **實作邏輯**：
  - 遍歷所有 TrackingGroup 實例
  - 刷新每個群組的統計數據
  - 累計總訂單數、總盈虧、總手數
- **性能優化**：使用 HashMap 迭代器高效遍歷

#### `CalculateProfitStatistics()`
- **功能**：盈虧統計計算
- **實作邏輯**：
  - 遍歷所有群組，比較各群組的最大盈利和最大虧損
  - 更新全局最大盈利和最大虧損
- **數據處理**：正確處理正負值比較

#### `CalculateTimeStatistics()`
- **功能**：時間相關統計計算
- **實作邏輯**：
  - 找出所有群組中最早和最新的訂單時間
  - 計算加權平均持倉時間
- **時間處理**：正確處理 datetime 類型的比較和計算

#### `CalculateAverageValues()`
- **功能**：平均值計算
- **實作邏輯**：
  - 計算按手數加權的平均開倉價
  - 確定最早和最新訂單的價格
- **數學計算**：使用加權平均公式，確保精度

#### `CalculateMartingaleStatistics()`
- **功能**：Martingale 策略特定統計
- **實作邏輯**：
  - 統計活躍群組和空群組數量
  - 確定活躍群組 ID 和下一個可用 ID
  - 檢查是否需要生成新的群組 ID
- **策略邏輯**：符合 Martingale 策略的群組管理需求

#### `ResetMartingaleStatistics()`
- **功能**：重置 Martingale 統計數據
- **實作**：將所有 MartingaleStats 結構體欄位重置為初始值
- **用途**：在重新計算統計前清理舊數據

### 3. 輔助方法

#### `CreateId()`
- **功能**：生成唯一識別碼
- **算法**：基於魔術數字和當前群組數量生成基礎 ID
- **衝突處理**：檢查 ID 是否已存在，如存在則添加數字後綴
- **安全機制**：防止無限循環，最多嘗試 1000 次

#### `DetermineGroupId(int ticket, int magic)`
- **功能**：根據訂單屬性確定群組 ID
- **策略**：使用魔術數字和訂單開倉時間的小時數
- **擴展性**：可根據需要調整分組邏輯

#### `EnsureGroupExists(string groupId)`
- **功能**：確保群組存在，如果不存在則創建
- **驗證**：檢查群組 ID 有效性和群組數量限制
- **創建邏輯**：創建新的 TrackingGroup 並添加到映射中
- **錯誤處理**：處理創建失敗的情況

### 4. 受保護方法

#### `ExecuteTracking()`
- **功能**：執行訂單追蹤的虛擬方法
- **實作邏輯**：
  - 遍歷 TradingPool 中的所有訂單
  - 驗證訂單是否符合條件（魔術數字和交易符號）
  - 確定訂單所屬群組並確保群組存在
- **擴展性**：子類可覆寫以自定義追蹤行為

### 5. 公共方法

#### `GetReservedSize()`
- **功能**：獲取已分配群組數
- **實作**：直接返回 m_reservedSize 成員變量
- **用途**：外部查詢當前群組分配狀態

#### `GetGroup(string id)`
- **功能**：根據 ID 獲取特定群組
- **參數驗證**：檢查 ID 是否為空
- **查找邏輯**：使用 HashMap 的 contains 和 get 方法
- **錯誤處理**：ID 無效或不存在時返回 NULL 並輸出錯誤信息

#### `ClearAll()`
- **功能**：清除所有數據的私有方法
- **清理順序**：
  1. 遍歷並刪除所有 TrackingGroup 實例
  2. 清空 HashMap
  3. 重置計數器和統計數據
- **內存管理**：確保沒有內存洩漏

#### `Refresh()`
- **功能**：刷新群組狀態，執行完整的更新流程
- **執行順序**：ExecuteTracking() → ResetStatistics() → UpdateStatistics()
- **設計模式**：模板方法模式的具體實現

## 設計特點

### 1. 錯誤處理
- 所有方法都包含適當的參數驗證
- 提供詳細的繁體中文錯誤信息
- NULL 指針檢查和邊界檢查

### 2. 性能優化
- 使用 HashMap 提供 O(1) 的群組查找
- 利用迭代器模式高效遍歷
- 避免重複的統計計算

### 3. 內存管理
- 正確的建構函數和解構函數實作
- 動態分配對象的生命週期管理
- 防止內存洩漏

### 4. 擴展性
- 虛擬方法支持子類覆寫
- 模塊化的統計計算方法
- 靈活的群組 ID 生成策略

### 5. MQL4 語法規範
- 嚴格遵循 MQL4 語法規範
- 正確使用指針和引用
- 適當的類型轉換和函數調用

## 測試

已創建 MartingaleGroupTest.mq4 測試文件，包含：
- 建構函數測試
- 基本功能測試
- 統計計算測試
- 群組管理測試
- 錯誤處理測試

## 總結

MartingaleGroup 類別的實作完整地支持了 Martingale 策略的群組管理需求，提供了：
- 完整的統計計算功能
- 靈活的群組管理機制
- 強健的錯誤處理
- 良好的性能和擴展性

所有方法都遵循 MQL4 語法規範，使用繁體中文註解，並與現有的 TradingStatistics 基類和相關結構體良好整合。
