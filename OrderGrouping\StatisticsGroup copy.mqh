//+------------------------------------------------------------------+
//| TrackingGroup.mqh                                                 |
//| Enhanced OrderGroup class with statistics and pool integration  |
//| Lightweight version without framework dependencies              |
//+------------------------------------------------------------------+
#property copyright "MQL4 Library"
#property version   "1.00"
#property strict

#include "../../mql4_module/mql4-lib/Trade/OrderGroup.mqh"
#include "../mql4-lib/Trade/OrderPool.mqh"
#include "../mql4-lib/Collection/HashMap.mqh"
#include "MatcherBuilder.mqh"

//+------------------------------------------------------------------+
//| Order group statistics structure                                |
//+------------------------------------------------------------------+
struct OrderGroupStats
{
    int               totalOrders;        // 總訂單數
    double            totalProfit;        // 總盈虧
    double            totalLots;          // 總手數
    double            avgOpenPrice;       // 平均開倉價
    double            maxProfit;          // 最大盈利
    double            maxLoss;            // 最大虧損
    double            oldestOrderPrice;   // 最早訂單開倉價
    double            newestOrderPrice;   // 最新訂單開倉價
    datetime          oldestOrderTime;    // 最早訂單時間
    datetime          newestOrderTime;    // 最新訂單時間
    int               avgHoldingTime;     // 平均持倉時間（秒）
};

//+------------------------------------------------------------------+
//| Martingale statistics structure                                 |
//+------------------------------------------------------------------+
struct MartingaleStats
{
    int               reservedGroups;     // 已分配群組數
    int               activeGroups;       // 活躍群組數
    int               emptyGroups;        // 空群組數
    string            activeId;           // 活躍群組編碼
    string            nextId;             // 下一個可用群組編碼
};

class TradingStatistics
{
private:
    // Statistics
    OrderGroupStats   m_stats;           // 群組統計

protected:
    //+------------------------------------------------------------------+
    //| 受保護方法 - 內部流程，子類可擴展                                |
    //+------------------------------------------------------------------+

    bool                      SetStatistics(OrderGroupStats &stats);
    void                      ResetStatistics();

public:
    /// 獲取群組統計信息 - 主要查詢接口
    OrderGroupStats           GetStatistics() { return m_stats; }

    /// 刷新狀態 - 主要操作接口，執行完整的更新流程
    virtual void              Refresh() = 0; // 子類實現
};

//+------------------------------------------------------------------+
//| TradingStatistics class implementation                          |
//+------------------------------------------------------------------+
bool TradingStatistics::SetStatistics(OrderGroupStats &stats)
{
    if(stats.totalOrders < 0)
    {
        Print("Error: Invalid total orders count");
        return false;
    }

    if(stats.totalLots < 0)
    {
        Print("Error: Invalid total lots amount");
        return false;
    }

    if(stats.avgOpenPrice < 0)
    {
        Print("Error: Invalid average open price");
        return false;
    }

    if(stats.oldestOrderPrice < 0)
    {
        Print("Error: Invalid oldest order price");
        return false;
    }

    if(stats.newestOrderPrice < 0)
    {
        Print("Error: Invalid newest order price");
        return false;
    }

    if(stats.oldestOrderTime < 0)
    {
        Print("Error: Invalid oldest order time");
        return false;
    }

    if(stats.newestOrderTime < 0)
    {
        Print("Error: Invalid newest order time");
        return false;
    }

    if(stats.avgHoldingTime < 0)
    {
        Print("Error: Invalid average holding time");
        return false;
    }

    m_stats = stats;
    return true;
}

//+------------------------------------------------------------------+
//| Reset statistics                                                |
//+------------------------------------------------------------------+
void TradingStatistics::ResetStatistics()
{
    m_stats.totalOrders = 0;
    m_stats.totalProfit = 0.0;
    m_stats.totalLots = 0.0;
    m_stats.avgOpenPrice = 0.0;
    m_stats.maxProfit = 0.0;
    m_stats.maxLoss = 0.0;
    m_stats.oldestOrderPrice = 0.0;
    m_stats.newestOrderPrice = 0.0;
    m_stats.oldestOrderTime = 0;
    m_stats.newestOrderTime = 0;
    m_stats.avgHoldingTime = 0;
}

//+------------------------------------------------------------------+
//| Enhanced OrderGroup class with statistics and pool integration  |
//+------------------------------------------------------------------+
class TrackingGroup : public TradingStatistics
{
private:
    // Trading pool integration
    string            m_symbol;          // 交易符號
    int               m_magicNumber;     // 魔術數字

    OrderGroup*       m_group;           // 訂單群組
    TradingPool*      m_pool;            // 交易池

    //+------------------------------------------------------------------+
    //| 私有內部方法 - 純實現細節                                        |
    //+------------------------------------------------------------------+

    void              InitializeGroup();      // 初始化 OrderGroup
    void              InitializePool();       // 初始化 TradingPool
    void              UpdateStatistics();     // 重新計算統計數據

    //+------------------------------------------------------------------+
    //| 統計計算分解方法 - 利用 groupDoubleProperty                      |
    //+------------------------------------------------------------------+
    void              CalculateBasicStatistics();     // 基本統計（利用基類方法）
    void              CalculateProfitStatistics();    // 盈虧統計
    void              CalculateTimeStatistics();      // 時間統計
    void              CalculateAverageValues();       // 平均值計算

protected:
    //+------------------------------------------------------------------+
    //| 受保護方法 - 內部流程，子類可擴展                                |
    //+------------------------------------------------------------------+

    /// 執行訂單追蹤 - 子類可覆寫以自定義追蹤行為
    virtual void      ExecuteTracking();

public:
    //+------------------------------------------------------------------+
    //| 公共接口 - 外部可直接使用                                        |
    //+------------------------------------------------------------------+

    // 建構函數和解構函數
                      TrackingGroup(string symbol = "", int magicNumber = 0);
    virtual          ~TrackingGroup();

    /// 刷新群組狀態 - 主要操作接口，執行完整的更新流程
    virtual void      Refresh() override { ClearAll(); ExecuteTracking(); ResetStatistics(); UpdateStatistics(); }

    //+------------------------------------------------------------------+
    //| 公共接口 - 外部可直接使用                                        |
    //+------------------------------------------------------------------+

public:
    int               GetOrderCount() { return m_group != NULL ? m_group.size() : 0; }
    int               GetTicket(int index) { return m_group != NULL ? m_group.get(index) : -1; }

    //+------------------------------------------------------------------+
    //| 私有接口 - 外部不可直接使用                                    |
    //+------------------------------------------------------------------+

private:
    void              ClearAll();
};

//+------------------------------------------------------------------+
//| Constructor with symbol and magic number                        |
//+------------------------------------------------------------------+
TrackingGroup::TrackingGroup(string symbol = "", int magicNumber = 0)
    : TradingStatistics()
{
    m_symbol = symbol == "" ? Symbol() : symbol;
    m_magicNumber = magicNumber;

    m_group = NULL;
    m_pool = NULL;

    // 初始化 OrderGroup
    InitializeGroup();

    // 如果提供了 magicNumber，創建對應的 TradingPool
    if (magicNumber > 0)
    {
        InitializePool();
    }
}



//+------------------------------------------------------------------+
//| Destructor                                                      |
//+------------------------------------------------------------------+
TrackingGroup::~TrackingGroup()
{
    // 總是刪除我們創建的對象（在構造函數中創建）
    if (m_group != NULL)
    {
        delete m_group;
        m_group = NULL;
    }

    if (m_pool != NULL)
    {
        delete m_pool;
        m_pool = NULL;
    }
}

//+------------------------------------------------------------------+
//| Initialize OrderGroup                                           |
//+------------------------------------------------------------------+
void TrackingGroup::InitializeGroup()
{
    if (m_group == NULL)
    {
        // 創建 OrderGroup 實例，使用指定的交易符號
        m_group = new OrderGroup(m_symbol);

        if (m_group == NULL)
        {
            Print("錯誤：無法創建 OrderGroup 實例，符號：", m_symbol);
        }
    }
}

//+------------------------------------------------------------------+
//| Initialize TradingPool                                          |
//+------------------------------------------------------------------+
void TrackingGroup::InitializePool()
{
    if (m_pool == NULL)
    {
        MatcherBuilder builder;
        OrderMatcher* matcher = builder.withSymbolMatcher(m_symbol)
                                       .withMagicNumberMatcher(m_magicNumber)
                                       .build();
        m_pool = new TradingPool(matcher);

        if (m_pool == NULL)
        {
            Print("錯誤：無法創建 TradingPool 實例，符號：", m_symbol, "，魔術數字：", m_magicNumber);
        }
    }
}









//+------------------------------------------------------------------+
//| 更新統計數據 - 重構版本（方案一）                               |
//|                                                                  |
//| 功能說明：                                                       |
//| - 使用方法拆分重構，將複雜邏輯分解為職責單一的小方法             |
//| - 充分利用 groupDoubleProperty 和基類方法                       |
//| - 提高代碼可讀性、可測試性和可維護性                             |
//+------------------------------------------------------------------+
void TrackingGroup::UpdateStatistics()
{

    // 檢查 OrderGroup 是否已初始化
    if (m_group == NULL)
    {
        Print("警告：OrderGroup 未初始化，無法更新統計數據");
        return;
    }

    const int orderCount = m_group.size();
    if (orderCount == 0) return;

    // 分別計算不同類型的統計
    CalculateBasicStatistics();
    CalculateProfitStatistics();
    CalculateTimeStatistics();
    CalculateAverageValues();
}

//+------------------------------------------------------------------+
//| 計算基本統計 - 利用 groupDoubleProperty 和基類方法              |
//|                                                                  |
//| 功能說明：                                                       |
//| - 使用基類的 groupProfit() 和 groupLots() 方法                 |
//| - 利用 groupDoubleProperty 進行高效統計計算                     |
//| - 避免重複的訂單選擇和屬性訪問                                   |
//+------------------------------------------------------------------+
void TrackingGroup::CalculateBasicStatistics()
{
    if (m_group == NULL) return;

    OrderGroupStats stats = GetStatistics();

    // 利用 OrderGroup 的高效方法計算總計
    stats.totalProfit = m_group.groupProfit();  // 使用 groupDoubleProperty(Order::Profit)
    stats.totalLots = m_group.groupLots();      // 使用 groupDoubleProperty(Order::Lots)

    SetStatistics(stats);
}

//+------------------------------------------------------------------+
//| 計算盈虧統計 - 找出最大盈利和最大虧損                           |
//|                                                                  |
//| 功能說明：                                                       |
//| - 遍歷所有訂單找出極值                                           |
//| - 只進行必要的訂單選擇和屬性訪問                                 |
//+------------------------------------------------------------------+
void TrackingGroup::CalculateProfitStatistics()
{
    if (m_group == NULL) return;

    const int orderCount = m_group.size();

    OrderGroupStats stats = GetStatistics();

    for (int i = 0; i < orderCount; i++)
    {
        if (!Order::Select(m_group.get(i))) continue;

        const double profit = Order::Profit();

        // 更新極值
        if (profit > stats.maxProfit)
            stats.maxProfit = profit;
        if (profit < stats.maxLoss)
            stats.maxLoss = profit;
    }

    SetStatistics(stats);
}

//+------------------------------------------------------------------+
//| 計算時間統計 - 找出最早和最新訂單的時間和價格                   |
//|                                                                  |
//| 功能說明：                                                       |
//| - 找出最早和最新的訂單開倉時間                                   |
//| - 記錄對應的開倉價格                                             |
//| - 計算開倉訂單的平均持倉時間                                     |
//+------------------------------------------------------------------+
void TrackingGroup::CalculateTimeStatistics()
{
    if (m_group == NULL) return;

    const int orderCount = m_group.size();
    const datetime currentTime = TimeCurrent();

    OrderGroupStats stats = GetStatistics();
    stats.oldestOrderTime = UINT_MAX;
    stats.newestOrderTime = 0;

    int holdingTimeSum = 0;
    int openOrderCount = 0;

    for (int i = 0; i < orderCount; i++)
    {
        if (!Order::Select(m_group.get(i))) continue;

        const datetime openTime = Order::OpenTime();
        const double openPrice = Order::OpenPrice();
        const datetime closeTime = Order::CloseTime();

        // 更新最早訂單信息
        if (openTime < stats.oldestOrderTime)
        {
            stats.oldestOrderTime = openTime;
            stats.oldestOrderPrice = openPrice;
        }

        // 更新最新訂單信息
        if (openTime > stats.newestOrderTime)
        {
            stats.newestOrderTime = openTime;
            stats.newestOrderPrice = openPrice;
        }

        // 計算持倉時間（僅開倉訂單）
        if (closeTime == 0)
        {
            holdingTimeSum += (int)(currentTime - openTime);
            openOrderCount++;
        }
    }

    // 計算平均持倉時間
    stats.avgHoldingTime = (openOrderCount > 0) ? holdingTimeSum / openOrderCount : 0;

    SetStatistics(stats);
}

//+------------------------------------------------------------------+
//| 計算平均值 - 利用基類的 groupAvg() 方法                         |
//|                                                                  |
//| 功能說明：                                                       |
//| - 使用基類的 groupAvg() 方法計算加權平均開倉價                  |
//| - 該方法已經實現了按手數加權的平均價格計算                       |
//| - 避免重複實現相同的邏輯                                         |
//+------------------------------------------------------------------+
void TrackingGroup::CalculateAverageValues()
{
    if (m_group == NULL) return;

    OrderGroupStats stats = GetStatistics();
    // 利用 OrderGroup 的高效加權平均計算
    stats.avgOpenPrice = m_group.groupAvg();  // 按手數加權的平均開倉價

    SetStatistics(stats);
}

//+------------------------------------------------------------------+
//| 執行訂單追蹤 - 子類可覆寫以自定義追蹤行為                        |
//+------------------------------------------------------------------+
void TrackingGroup::ExecuteTracking()
{
    if (m_group == NULL || m_pool == NULL)
    {
        Print("警告：OrderGroup 或 TradingPool 未初始化，無法執行追蹤");
        return;
    }

    // 使用 TradingPool 遍歷符合條件的訂單
    foreachorder(m_pool)
    {
        int ticket = Order::Ticket();
        if (ticket > 0)
        {
            m_group.add(ticket);
        }
    }
}

//+------------------------------------------------------------------+
//| 清空所有訂單                                                     |
//+------------------------------------------------------------------+
void TrackingGroup::ClearAll()
{
    if (m_group == NULL)
    {
        Print("警告：OrderGroup 未初始化，無法清空");
        return;
    }

    const int orderCount = m_group.size();

    // 清空 OrderGroup 中的所有訂單
    m_group.clear();
}

//+------------------------------------------------------------------+
//| Martingale group class with statistics and pool integration  |
//+------------------------------------------------------------------+
class MartingaleGroup : public TradingStatistics
{
private:
    // Trading pool integration
    string                          m_symbol;          // 交易符號
    int                             m_magicNumber;     // 魔術數字

    HashMap<string, TrackingGroup*> m_orderMap;        // 訂單映射（magicNumber+index -> key）
    int                             m_reservedSize;    // 已分配群組數
    int                             m_maxOrders;       // 最大訂單數
    int                             m_maxLevels;       // 最大級別
    TradingPool*                    m_pool;            // 交易池

    // Statistics
    MartingaleStats            m_stats;           // 群組統計

    //+------------------------------------------------------------------+
    //| 私有內部方法 - 純實現細節                                        |
    //+------------------------------------------------------------------+

    void              InitializeMap();      // 初始化 訂單映射
    void              InitializePool();     // 初始化 交易池

    //+------------------------------------------------------------------+
    //| 私有內部方法 - 純實現細節                                        |
    //+------------------------------------------------------------------+

    void              UpdateStatistics();     // 重新計算統計數據
    void              UpdateMartingaleStatistics(); // 重新計算 Martingale 統計數據

    //+------------------------------------------------------------------+
    //| 統計計算分解方法 - 利用 groupDoubleProperty                      |
    //+------------------------------------------------------------------+
    void              CalculateBasicStatistics();     // 基本統計（利用基類方法）
    void              CalculateProfitStatistics();    // 盈虧統計
    void              CalculateTimeStatistics();      // 時間統計
    void              CalculateAverageValues();       // 平均值計算

    //+------------------------------------------------------------------+
    //| 統計計算分解方法 - 利用 groupDoubleProperty                      |
    //+------------------------------------------------------------------+
    void              CalculateMartingaleStatistics(); // Martingale 統計

    //+------------------------------------------------------------------+
    //| 統計重置方法 - 利用 groupDoubleProperty                      |
    //+------------------------------------------------------------------+
    void              ResetMartingaleStatistics();

    //+------------------------------------------------------------------+
    //| 其他輔助方法 - 純實現細節                                        |
    //+------------------------------------------------------------------+
    string            CreateId(int magic_number, int index);    // 生成唯一識別碼 magic+index

protected:
    //+------------------------------------------------------------------+
    //| 受保護方法 - 內部流程，子類可擴展                                |
    //+------------------------------------------------------------------+

    /// 執行訂單追蹤 - 子類可覆寫以自定義追蹤行為
    virtual void      ExecuteTracking();

public:
    //+------------------------------------------------------------------+
    //| 公共接口 - 外部可直接使用                                        |
    //+------------------------------------------------------------------+

    // 建構函數和解構函數
                      MartingaleGroup(string symbol = "", int magicNumber = 0, int maxOrders = 20, int maxLevels = 4);
    virtual          ~MartingaleGroup();

    /// 獲取群組統計信息 - 主要查詢接口
    MartingaleStats   GetMartingaleStatistics() { return m_stats; }

    /// 刷新群組狀態 - 主要操作接口，執行完整的更新流程
    virtual void      Refresh() override;

    //+------------------------------------------------------------------+
    //| 公共接口 - 外部可直接使用                                        |
    //+------------------------------------------------------------------+

public:
    int                 GetReservedSize();
    TradingStatistics*  GetGroup(string id);

    //+------------------------------------------------------------------+
    //| 私有接口 - 外部不可直接使用                                    |
    //+------------------------------------------------------------------+

private:
    void              ClearAll();
};
